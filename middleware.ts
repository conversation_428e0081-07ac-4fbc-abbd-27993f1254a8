import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    // 检查用户是否有必需的角色
    const token = req.nextauth.token as any;

    const requiredRole = process.env.KEYCLOAK_REQUIRED_ROLE;

    if (
      token &&
      requiredRole &&
      Array.isArray(token.roles) &&
      !token.roles.includes(requiredRole)
    ) {
      // 用户已登录但没有必需的角色，重定向到登录页面并显示错误
      return NextResponse.redirect(
        new URL("/auth/signin?error=insufficient_role", req.url)
      );
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token }) => {
        // 若未登录或刷新失败则视为未授权，避免受保护页面与登录页来回跳转
        if (!token) return false;
        if ((token as any).error === "RefreshAccessTokenError") return false;
        return true;
      },
    },
  }
);

// 配置需要保护的路由
export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了以下开头的路径：
     * - api/auth (NextAuth API 路由)
     * - auth (认证页面)
     * - _next/static (静态文件)
     * - _next/image (图片优化文件)
     * - favicon.ico (网站图标)
     * - public 文件夹中的文件
     */
    "/((?!api/auth|auth|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)",
  ],
};
