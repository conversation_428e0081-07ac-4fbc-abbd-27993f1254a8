import { NextRequest, NextResponse } from 'next/server';
import { withSimpleAuth, SimpleAuthenticatedRequest } from '@/lib/simple-auth';
import { getAuthorizedClient, listMessagesForAddress } from '@/lib/gmail';
import { extractVerificationCodes, getLatestVerificationCode } from '@/lib/verification-codes';

async function handleGetVerificationCodes(request: SimpleAuthenticatedRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const latest = searchParams.get('latest') === 'true';
    const timeWindow = parseInt(searchParams.get('timeWindow') || '5'); // 默认5分钟

    if (!email) {
      return NextResponse.json(
        { success: false, error: '邮箱地址参数缺失' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: '邮箱地址格式无效' },
        { status: 400 }
      );
    }

    try {
      // 获取Gmail授权客户端
      const auth = await getAuthorizedClient();
      const messages = await listMessagesForAddress(auth, email, 50); // 获取最多50封邮件

      if (latest) {
        // 只返回最新的验证码
        const latestCode = getLatestVerificationCode(messages, timeWindow);
        return NextResponse.json({ 
          success: true, 
          email,
          latest_code: latestCode,
          time_window_minutes: timeWindow
        });
      } else {
        // 返回所有验证码
        const codes = extractVerificationCodes(messages, timeWindow);
        return NextResponse.json({ 
          success: true, 
          email,
          codes,
          count: codes.length,
          time_window_minutes: timeWindow
        });
      }
    } catch (gmailError) {
      console.error('Gmail API error:', gmailError);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Gmail API 访问失败',
          details: 'Gmail 服务可能未正确配置或授权已过期'
        },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error('Error fetching verification codes:', error);
    return NextResponse.json(
      { success: false, error: '获取验证码失败' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return withSimpleAuth(request, handleGetVerificationCodes);
}
