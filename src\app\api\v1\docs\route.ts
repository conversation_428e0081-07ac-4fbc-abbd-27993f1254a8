import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:9042';
  
  const docs = {
    title: "One Mail API Documentation",
    version: "1.0.0",
    description: "API for generating temporary emails and retrieving verification codes",
    base_url: baseUrl,
    authentication: {
      type: "API Key",
      header: "Authorization",
      format: "Bearer <password> or <password>",
      note: "Password is configured in API_AUTH_PASSWORD environment variable"
    },
    endpoints: [
      {
        method: "POST",
        path: "/api/v1/generate-email",
        description: "Generate a new temporary email address",
        authentication: "Required",
        parameters: "None",
        request_body: "None",
        response: {
          success: {
            success: true,
            email: "<EMAIL>",
            created_at: "2024-01-01T12:00:00.000Z"
          },
          error: {
            success: false,
            error: "Error message"
          }
        },
        example: {
          curl: `curl -X POST "${baseUrl}/api/v1/generate-email" \\
  -H "Authorization: Bearer YOUR_PASSWORD"`
        }
      },
      {
        method: "GET",
        path: "/api/v1/verification-codes",
        description: "Get verification codes for a specific email address",
        authentication: "Required",
        parameters: [
          {
            name: "email",
            type: "string",
            required: true,
            description: "Email address to check for verification codes"
          },
          {
            name: "latest",
            type: "boolean",
            required: false,
            default: false,
            description: "If true, returns only the latest verification code"
          },
          {
            name: "timeWindow",
            type: "integer",
            required: false,
            default: 5,
            description: "Time window in minutes to search for codes"
          }
        ],
        response: {
          success_all: {
            success: true,
            email: "<EMAIL>",
            codes: [
              {
                code: "123456",
                email: "<EMAIL>",
                subject: "Welcome to Augment Code",
                date: "2024-01-01T12:00:00.000Z"
              }
            ],
            count: 1,
            time_window_minutes: 5
          },
          success_latest: {
            success: true,
            email: "<EMAIL>",
            latest_code: {
              code: "123456",
              email: "<EMAIL>",
              subject: "Welcome to Augment Code",
              date: "2024-01-01T12:00:00.000Z"
            },
            time_window_minutes: 5
          },
          error: {
            success: false,
            error: "Error message"
          }
        },
        examples: {
          get_all_codes: {
            curl: `curl -X GET "${baseUrl}/api/v1/verification-codes?email=<EMAIL>" \\
  -H "Authorization: Bearer YOUR_PASSWORD"`
          },
          get_latest_code: {
            curl: `curl -X GET "${baseUrl}/api/v1/verification-codes?email=<EMAIL>&latest=true" \\
  -H "Authorization: Bearer YOUR_PASSWORD"`
          },
          custom_time_window: {
            curl: `curl -X GET "${baseUrl}/api/v1/verification-codes?email=<EMAIL>&timeWindow=10" \\
  -H "Authorization: Bearer YOUR_PASSWORD"`
          }
        }
      }
    ],
    error_codes: {
      "400": "Bad Request - Invalid parameters",
      "401": "Unauthorized - Invalid or missing authentication",
      "500": "Internal Server Error - Server error",
      "503": "Service Unavailable - Gmail API not available"
    },
    usage_flow: [
      "1. Configure API_AUTH_PASSWORD in your environment variables",
      "2. Call POST /api/v1/generate-email to get a new temporary email",
      "3. Use the email address for registration/verification",
      "4. Call GET /api/v1/verification-codes with the email to get verification codes",
      "5. Use latest=true parameter to get only the most recent code"
    ]
  };

  return NextResponse.json(docs, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
