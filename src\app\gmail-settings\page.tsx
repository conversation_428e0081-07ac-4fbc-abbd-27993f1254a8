"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import Layout from "@/components/Layout";

interface GmailAuthStatus {
  isAuthenticated: boolean;
}

export default function GmailSettingsPage() {
  const [authStatus, setAuthStatus] = useState<GmailAuthStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const searchParams = useSearchParams();
  const { data: session } = useSession();

  // Check for URL parameters
  useEffect(() => {
    const errorParam = searchParams.get('error');
    const successParam = searchParams.get('success');

    if (errorParam) {
      switch (errorParam) {
        case 'oauth_error':
          setError('Gmail 授权失败，请重试。');
          break;
        case 'no_code':
          setError('未收到授权码，请重试。');
          break;
        case 'token_exchange_failed':
          setError('令牌交换失败，请重试。');
          break;
        case 'callback_error':
          setError('回调处理失败，请重试。');
          break;
        default:
          setError('发生未知错误，请重试。');
      }
    }

    if (successParam === 'true') {
      setSuccess('Gmail 账户连接成功！');
      // Refresh auth status
      checkAuthStatus();
    }
  }, [searchParams]);

  // Check Gmail authentication status
  const checkAuthStatus = async () => {
    if (!session) return;

    try {
      const response = await fetch('/api/gmail/auth-status');
      const data = await response.json();

      if (data.success) {
        setAuthStatus({ isAuthenticated: data.isAuthenticated });
      } else {
        setError('检查认证状态失败');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setError('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  // Connect Gmail account
  const connectGmail = async () => {
    setConnecting(true);
    setError(null);

    try {
      const response = await fetch('/api/gmail/auth-url');
      const data = await response.json();

      if (data.success) {
        // Redirect to Google OAuth
        window.location.href = data.authUrl;
      } else {
        setError('获取认证链接失败');
      }
    } catch (error) {
      console.error('Error getting auth URL:', error);
      setError('网络错误，请重试');
    } finally {
      setConnecting(false);
    }
  };

  useEffect(() => {
    if (session) {
      checkAuthStatus();
    }
  }, [session]);

  if (!session) {
    return (
      <Layout>
        <div className="p-6">
          <div className="text-center">
            <p className="text-gray-600">请先登录以访问 Gmail 设置。</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="p-6">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Gmail 设置</h1>

          {/* Status Messages */}
          {error && (
            <div className="mb-6 rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">错误</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-6 rounded-md bg-green-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">成功</h3>
                  <div className="mt-2 text-sm text-green-700">
                    <p>{success}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Gmail Connection Status */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Gmail 账户连接</h2>
            
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
                <span className="text-gray-600">检查连接状态...</span>
              </div>
            ) : authStatus ? (
              <div>
                {authStatus.isAuthenticated ? (
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">Gmail 账户已连接</p>
                      <p className="text-sm text-green-700">您可以正常使用邮件功能。</p>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center mb-4">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800">Gmail 账户未连接</p>
                        <p className="text-sm text-red-700">需要连接 Gmail 账户才能使用邮件功能。</p>
                      </div>
                    </div>
                    
                    <button
                      onClick={connectGmail}
                      disabled={connecting}
                      className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white font-bold py-2 px-4 rounded"
                    >
                      {connecting ? '连接中...' : '连接 Gmail 账户'}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-red-600">
                <p>无法获取连接状态</p>
                <button
                  onClick={checkAuthStatus}
                  className="mt-2 bg-gray-600 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded text-sm"
                >
                  重试
                </button>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-6 bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-medium text-blue-900 mb-2">使用说明</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 点击"连接 Gmail 账户"按钮开始授权流程</li>
              <li>• 您将被重定向到 Google 的授权页面</li>
              <li>• 登录您的 Google 账户并授权应用访问您的 Gmail</li>
              <li>• 授权成功后，您将被重定向回此页面</li>
              <li>• 连接成功后，您就可以在主页面查看邮件了</li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
}
