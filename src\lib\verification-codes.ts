import { EmailMessage } from './gmail';

export interface VerificationCode {
  code: string;
  email: string;
  subject: string;
  date: string;
}

/**
 * 从邮件列表中提取验证码
 * @param emails 邮件列表
 * @param timeWindowMinutes 时间窗口（分钟），默认5分钟
 * @returns 验证码列表，按时间倒序排列
 */
export function extractVerificationCodes(
  emails: EmailMessage[], 
  timeWindowMinutes: number = 5
): VerificationCode[] {
  const codes: VerificationCode[] = [];
  const timeWindowAgo = new Date(Date.now() - timeWindowMinutes * 60 * 1000);

  emails.forEach((email) => {
    const emailDate = new Date(email.date);

    // 检查是否在指定时间窗口内收到
    if (emailDate >= timeWindowAgo) {
      // 检查标题或内容是否包含关键词
      const hasWelcome = email.subject
        .toLowerCase()
        .includes("welcome to augment code");
      const hasVerificationCode = email.snippet
        .toLowerCase()
        .includes("verification code");

      if (hasWelcome || hasVerificationCode) {
        // 首先尝试匹配 "verification code is: 123456" 格式
        let codeMatch = email.snippet.match(
          /verification code is:\s*(\d{6})/i
        );

        if (!codeMatch) {
          // 如果没找到，搜索任何连续的6位数字
          codeMatch = email.snippet.match(/\b(\d{6})\b/);
        }

        if (codeMatch) {
          const code = codeMatch[1];
          codes.push({
            code,
            email: email.to,
            subject: email.subject,
            date: email.date,
          });
        }
      }
    }
  });

  // 按时间排序，最新的在前
  codes.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return codes;
}

/**
 * 获取最新的验证码
 * @param emails 邮件列表
 * @param timeWindowMinutes 时间窗口（分钟），默认5分钟
 * @returns 最新的验证码，如果没有则返回null
 */
export function getLatestVerificationCode(
  emails: EmailMessage[], 
  timeWindowMinutes: number = 5
): VerificationCode | null {
  const codes = extractVerificationCodes(emails, timeWindowMinutes);
  return codes.length > 0 ? codes[0] : null;
}
