import { NextRequest, NextResponse } from 'next/server';
import { withSimpleAuth, SimpleAuthenticatedRequest } from '@/lib/simple-auth';
import { db } from '@/lib/db';
import { generatedEmails } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function handleGenerateEmail(request: SimpleAuthenticatedRequest) {
  try {
    // 读取名字列表
    const namesFilePath = path.join(process.cwd(), 'namelist', 'names.txt');
    const namesContent = fs.readFileSync(namesFilePath, 'utf-8');
    const names = namesContent.split('\n').filter(name => name.trim());

    let email: string;
    let attempts = 0;
    const maxAttempts = 100;

    // 尝试生成唯一的邮箱地址
    do {
      // 随机选择一个名字
      const randomName = names[Math.floor(Math.random() * names.length)];

      // 生成随机数字后缀
      const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

      // 组合邮箱前缀，移除空格并转换为小写
      const emailPrefix = `${randomName.toLowerCase().replace(/\s+/g, '')}${randomSuffix}`;

      email = emailPrefix + '@techexpresser.com';

      // 检查数据库中是否已存在
      const existing = await db.select().from(generatedEmails).where(eq(generatedEmails.email, email)).limit(1);

      if (existing.length === 0) {
        break; // 找到唯一的邮箱
      }

      attempts++;
    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      return NextResponse.json(
        { success: false, error: '无法生成唯一邮箱，请重试' },
        { status: 500 }
      );
    }

    // 保存到数据库
    const [newEmail] = await db.insert(generatedEmails).values({
      email,
    }).returning();

    return NextResponse.json({
      success: true,
      email: newEmail.email,
      created_at: newEmail.createdAt,
    });
  } catch (error) {
    console.error('Error generating email:', error);
    return NextResponse.json(
      { success: false, error: '生成邮箱失败' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  return withSimpleAuth(request, handleGenerateEmail);
}
