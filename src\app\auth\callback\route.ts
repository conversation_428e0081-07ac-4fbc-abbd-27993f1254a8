import { NextRequest, NextResponse } from 'next/server';
import { getAuthorizedClient } from '@/lib/gmail';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const error = searchParams.get('error');

    // Determine base URL from environment (prefer server env)
    const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:9042';

    // Debug logs to verify env loading and proxy behavior
    console.log('[Gmail Callback] request.url =', request.url);
    console.log('[Gmail Callback] baseUrl (env) =', baseUrl);

    if (error) {
      console.error('Gmail OAuth error:', error);
      return NextResponse.redirect(new URL('/gmail-settings?error=oauth_error', baseUrl));
    }

    if (!code) {
      return NextResponse.redirect(new URL('/gmail-settings?error=no_code', baseUrl));
    }

    try {
      // Exchange code for tokens
      await getAuthorizedClient(code);

      // Redirect to success page (use env-based baseUrl to avoid localhost from proxy)
      return NextResponse.redirect(new URL('/gmail-settings?success=true', baseUrl));
    } catch (authError) {
      console.error('Error exchanging code for tokens:', authError);
      return NextResponse.redirect(new URL('/gmail-settings?error=token_exchange_failed', baseUrl));
    }
  } catch (error) {
    console.error('Error in Gmail callback:', error);
    const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:9042';
    return NextResponse.redirect(new URL('/gmail-settings?error=callback_error', baseUrl));
  }
}
