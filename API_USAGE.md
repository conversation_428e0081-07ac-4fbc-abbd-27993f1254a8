# One Mail API 使用指南

## 概述

One Mail 现在提供了简单的REST API，可以用于生成临时邮箱和获取验证码。这些API使用简单的密码认证，适合集成到其他系统中。

## 认证

所有API端点都需要在请求头中包含认证信息：

```
Authorization: Bearer your-password
```

或者简单格式：

```
Authorization: your-password
```

密码在环境变量 `API_AUTH_PASSWORD` 中配置。

## API端点

### 1. 生成新邮箱

**端点**: `POST /api/v1/generate-email`

**描述**: 生成一个新的临时邮箱地址并保存到数据库

**请求示例**:
```bash
curl -X POST "http://localhost:9042/api/v1/generate-email" \
  -H "Authorization: Bearer your-api-password-here"
```

**响应示例**:
```json
{
  "success": true,
  "email": "<EMAIL>",
  "created_at": "2025-08-14T17:55:22.185Z"
}
```

### 2. 获取验证码

**端点**: `GET /api/v1/verification-codes`

**描述**: 获取指定邮箱的验证码

**参数**:
- `email` (必需): 邮箱地址
- `latest` (可选): 如果为 `true`，只返回最新的验证码
- `timeWindow` (可选): 时间窗口（分钟），默认5分钟

**请求示例**:

获取所有验证码：
```bash
curl -X GET "http://localhost:9042/api/v1/verification-codes?email=<EMAIL>" \
  -H "Authorization: Bearer your-api-password-here"
```

只获取最新验证码：
```bash
curl -X GET "http://localhost:9042/api/v1/verification-codes?email=<EMAIL>&latest=true" \
  -H "Authorization: Bearer your-api-password-here"
```

**响应示例**:

所有验证码：
```json
{
  "success": true,
  "email": "<EMAIL>",
  "codes": [
    {
      "code": "123456",
      "email": "<EMAIL>",
      "subject": "Welcome to Augment Code",
      "date": "2025-08-14T17:55:22.185Z"
    }
  ],
  "count": 1,
  "time_window_minutes": 5
}
```

最新验证码：
```json
{
  "success": true,
  "email": "<EMAIL>",
  "latest_code": {
    "code": "123456",
    "email": "<EMAIL>",
    "subject": "Welcome to Augment Code",
    "date": "2025-08-14T17:55:22.185Z"
  },
  "time_window_minutes": 5
}
```

### 3. API文档

**端点**: `GET /api/v1/docs`

**描述**: 获取完整的API文档

**请求示例**:
```bash
curl -X GET "http://localhost:9042/api/v1/docs"
```

## 使用流程

1. **配置认证密码**: 在环境变量中设置 `API_AUTH_PASSWORD`
2. **生成邮箱**: 调用 `POST /api/v1/generate-email` 获取新的临时邮箱
3. **使用邮箱**: 在需要验证的服务中使用这个邮箱地址
4. **获取验证码**: 调用 `GET /api/v1/verification-codes` 获取验证码
5. **使用验证码**: 在验证服务中输入获取到的验证码

## 错误处理

API会返回以下HTTP状态码：

- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `500`: 服务器内部错误
- `503`: Gmail服务不可用

错误响应格式：
```json
{
  "success": false,
  "error": "错误描述"
}
```

## 环境配置

确保在 `.env.local` 文件中配置了以下变量：

```bash
# Simple API Authentication
API_AUTH_PASSWORD=your-api-password-here
```

## 注意事项

1. 验证码提取基于邮件内容中的关键词和模式匹配
2. 默认时间窗口为5分钟，可以通过 `timeWindow` 参数调整
3. Gmail API需要正确配置和授权
4. 生成的邮箱地址会自动保存到数据库中防止重复

## 在线文档

访问 `http://localhost:9042/api/v1/docs` 查看完整的API文档。
