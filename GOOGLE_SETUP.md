# Google Cloud Console 设置指南

## 概述

这个项目使用 Gmail API 来读取和显示邮件。您需要在 Google Cloud Console 中进行一些设置来启用 API 访问。

## 步骤 1: 创建 Google Cloud 项目（如果还没有）

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 点击项目选择器，然后点击"新建项目"
3. 输入项目名称（例如："Gmail Reader"）
4. 点击"创建"

## 步骤 2: 启用 Gmail API

1. 在 Google Cloud Console 中，确保您选择了正确的项目
2. 在左侧导航菜单中，点击"API 和服务" > "库"
3. 搜索 "Gmail API"
4. 点击 "Gmail API" 结果
5. 点击"启用"按钮

## 步骤 3: 创建 OAuth 2.0 凭据

1. 在左侧导航菜单中，点击"API 和服务" > "凭据"
2. 点击"+ 创建凭据" > "OAuth 客户端 ID"
3. 如果这是第一次创建 OAuth 客户端，您需要先配置 OAuth 同意屏幕：
   - 点击"配置同意屏幕"
   - 选择"外部"（除非您有 Google Workspace 账户）
   - 填写必需信息：
     - 应用名称：Gmail Reader
     - 用户支持电子邮件：您的邮箱
     - 开发者联系信息：您的邮箱
   - 点击"保存并继续"
   - 在"范围"页面，点击"保存并继续"
   - 在"测试用户"页面，添加您要测试的 Gmail 账户
   - 点击"保存并继续"

## 步骤 4: 配置 OAuth 客户端

1. 返回"凭据"页面
2. 点击"+ 创建凭据" > "OAuth 客户端 ID"
3. 选择应用类型："Web 应用"
4. 输入名称："Gmail Reader Web Client"
5. 在"已获授权的重定向 URI"部分，点击"+ 添加 URI"
6. 添加以下 URI：
   ```
   http://localhost:9042/auth/callback
   ```
7. 点击"创建"

## 步骤 5: 下载凭据文件

1. 创建完成后，会显示一个对话框，包含客户端 ID 和客户端密钥
2. 点击"下载 JSON"按钮
3. 将下载的文件重命名为 `google_oauth2.json`
4. 将文件放在项目的 `cert/` 目录中（替换现有文件）

## 步骤 6: 验证配置

您的 `cert/google_oauth2.json` 文件应该包含类似以下结构的内容：

```json
{
  "web": **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}
```

## 步骤 7: 运行应用

1. 确保开发服务器正在运行：

   ```bash
   npm run dev
   ```

2. 在浏览器中访问 `http://localhost:9042`

3. 点击"Connect Gmail Account"按钮

4. 您将被重定向到 Google 的授权页面

5. 登录您的 Google 账户并授权应用访问您的 Gmail

6. 授权成功后，您将被重定向回应用，并能看到您的邮件列表

## 生产环境配置

### 重要：生产环境部署

当部署到生产环境时，需要进行以下配置：

1. **更新环境变量**：

   ```bash
   # 在 .env.local 或生产环境配置中设置
   NEXTAUTH_URL=https://your-domain.com
   NEXT_PUBLIC_BASE_URL=https://your-domain.com
   ```

2. **更新 Google Cloud Console 重定向 URI**：

   - 在 Google Cloud Console 的 OAuth 客户端配置中添加生产环境的重定向 URI：

   ```
   https://your-domain.com/auth/callback
   ```

   - 保留开发环境的 URI 用于本地测试：

   ```
   http://localhost:9042/auth/callback
   ```

3. **代码会自动使用正确的域名**：
   - 开发环境：`http://localhost:9042/auth/callback`
   - 生产环境：`https://your-domain.com/auth/callback`

## 故障排除

### 错误："redirect_uri_mismatch"

- 确保在 Google Cloud Console 中添加的重定向 URI 与当前环境匹配
- 开发环境：检查端口号是否正确（应该是 9042）
- 生产环境：确保域名正确且使用 HTTPS

### 错误："access_denied"

- 确保您在 OAuth 同意屏幕的测试用户中添加了您的 Gmail 账户
- 检查应用是否处于"测试"模式

### 错误："invalid_client"

- 检查 `google_oauth2.json` 文件是否正确放置在 `cert/` 目录中
- 验证文件内容格式是否正确

### 应用显示"未验证"警告

- 这是正常的，因为应用处于测试模式
- 点击"高级" > "转到 Gmail Reader（不安全）"继续

## 安全注意事项

1. **不要提交凭据文件到版本控制系统**

   - `cert/google_oauth2.json` 包含敏感信息
   - 确保将 `cert/` 目录添加到 `.gitignore`

2. **生产环境部署**

   - 在生产环境中，需要将应用提交给 Google 进行验证
   - 更新重定向 URI 为您的生产域名

3. **访问令牌存储**
   - 当前实现将令牌存储在本地文件中
   - 在生产环境中，考虑使用更安全的存储方式

## 支持的功能

当前应用支持：

- ✅ Gmail 账户授权（长期有效）
- ✅ 自动 Token 刷新机制
- ✅ 服务器端渲染 (SSR)
- ✅ 读取邮件列表
- ✅ 显示邮件详情
- ✅ 查看邮件正文
- ✅ 区分已读/未读邮件

## 长期授权说明

### 🔄 自动刷新机制

应用已配置为获取长期有效的授权：

1. **Refresh Token**：通过 `access_type: 'offline'` 和 `prompt: 'consent'` 确保获得 refresh token
2. **自动刷新**：当 access token 即将过期时（提前 5 分钟），自动使用 refresh token 获取新的 access token
3. **长期有效**：只要定期使用应用，refresh token 可以保持有效数月甚至数年

### ⏰ 授权持续时间

- **Access Token**：通常 1 小时有效期
- **Refresh Token**：可能数月到数年有效（取决于使用频率）
- **自动续期**：每次使用时自动延长有效期

### 🔒 重新授权的情况

只有在以下情况下才需要重新授权：

- 用户手动撤销应用权限
- 长期不使用应用（6 个月以上）
- Google 检测到安全问题
- 应用权限范围发生变化

## 下一步

您可以扩展这个应用来支持：

- 发送邮件
- 邮件搜索
- 标签管理
- 附件下载
- 邮件归档/删除
